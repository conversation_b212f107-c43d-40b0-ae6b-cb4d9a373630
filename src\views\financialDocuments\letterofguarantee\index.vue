<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Button type="primary" @click="handleAdd" v-if="hasPermission([742])">新增</Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" :drop-down-actions="createDropDownActions(record)" :stopButtonPropagation="true" />
        </template>
        <template v-if="column.key === 'file'">
          <div v-for="(newVal, index) in record.file" :key="index">
            <a :href="newVal" target="_blank" @click="handlePreview(newVal, $event)">{{ `附件${index + 1}` }}</a></div
          >
        </template>
        <template v-if="column.key === 'other_file'">
          <div v-for="(newVal, index) in record.other_file" :key="index">
            <a :href="newVal" target="_blank" @click="handlePreview(newVal, $event)">{{ `附件${index + 1}` }}</a></div
          >
        </template>
        <template v-if="column.key === 'seal_file'">
          <div v-for="(newVal, index) in record.seal_file" :key="index">
            <a :href="newVal" target="_blank" @click="handlePreview(newVal, $event)">{{ `附件${index + 1}` }}</a></div
          >
        </template>
      </template>
    </BasicTable>
    <PreviewFile @register="registerModal" />
    <editDrawer @register="registereditDrawer" @success="reload" />
    <examineModal @register="registerexamineModal" @success="reload" />
    <stampModal @register="registerStampModal" @success="reload" />
  </div>
</template>
<script setup lang="ts">
import { message, Button } from 'ant-design-vue'
import { columns, columnsschemas } from './datas/data'
import { projectbkgetList } from '/@/api/erp/letterofguarantee'
import { ActionItem, BasicTable, EditRecordRow, useTable, TableAction } from '/@/components/Table'
import { createImgPreview } from '/@/components/Preview/index'
import { useModal } from '/@/components/Modal'
import PreviewFile from '/@/components/PreviewFile/xlsxFile.vue'
import editDrawer from './components/editDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
import { usePermission } from '/@/hooks/web/usePermission'
import examineModal from './components/examineModal.vue'
import stampModal from './components/stampModal.vue'

const { hasPermission } = usePermission()

const [registerModal, { openModal }] = useModal()
const [registerexamineModal, { openModal: openModalexamine, setModalProps: setModalexamine }] = useModal()
const [registerStampModal, { openModal: openStampModal }] = useModal()
const [registereditDrawer, { openDrawer, setDrawerProps }] = useDrawer()

const [registerTable, { reload }] = useTable({
  showIndexColumn: false,
  showTableSetting: true,
  useSearchForm: true,
  api: projectbkgetList,
  columns,
  actionColumn: {
    width: 250,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right'
  },
  formConfig: {
    schemas: columnsschemas,
    labelWidth: 150
  }
})

function createActions(record: EditRecordRow): ActionItem[] {
  let editButtonList: ActionItem[] = []

  if (record.is_finance === 1 || record.is_pm === 1 || record.is_pm_charger === 1) {
    return editButtonList
  }
  if (record.is_receip === 2) {
    if (record.is_finance === 0 && hasPermission([744])) {
      editButtonList.push({
        label: '财务审核',
        onClick: handleexamine.bind(null, record, 'finance', '财务审核'),
        ifShow: true
      })
    }
  } else if (record.is_receip === 1) {
    if (record.is_pm === 0 && hasPermission([745])) {
      editButtonList.push({
        label: '项目经理审核',
        onClick: handleexamine.bind(null, record, 'pm', '项目经理审核'),
        ifShow: true
      })
    } else if (record.is_pm === 2 && record.is_pm_charger === 0 && hasPermission([746])) {
      editButtonList.push({
        label: '项目经理负责人审核',
        onClick: handleexamine.bind(null, record, 'pm_charger', '项目经理负责人审核'),
        ifShow: true
      })
    } else if (record.is_pm === 2 && record.is_pm_charger === 2 && record.is_finance === 0 && hasPermission([744])) {
      editButtonList.push({
        label: '财务审核',
        onClick: handleexamine.bind(null, record, 'finance', '财务审核'),
        ifShow: true
      })
    }
  }

  if (record.is_finance === 0 && record.is_pm === 0 && record.is_pm_charger === 0 && hasPermission([743])) {
    editButtonList.push({
      label: '编辑',
      onClick: handleUpdate.bind(null, record),
      disabled: false,
      ifShow: true
    })
  }

  return editButtonList
}

function createDropDownActions(record: EditRecordRow): ActionItem[] {
  let editButtonList: ActionItem[] = [
    {
      label: '详情',
      onClick: handleDetail.bind(null, record),
      ifShow: hasPermission([113])
    },
    {
      label: '附件盖章',
      onClick: handleStamp.bind(null, record),
      ifShow: hasPermission([747])
    }
  ]

  return editButtonList
}

function handleAdd() {
  openDrawer(true, { type: 'add' })
  setDrawerProps({ title: '新增', showFooter: true, width: '50%' })
}

//更新
function handleUpdate(record) {
  openDrawer(true, {
    record,
    type: 'edit'
  })
  setDrawerProps({ title: '编辑', showFooter: true, width: '50%' })
}
//详情
function handleDetail(record) {
  setDrawerProps({ title: '详情', showFooter: false, width: '90%' })
  openDrawer(true, {
    record,
    type: 'detail'
  })
}

//单据审核
async function handleexamine(record: any, type: string, title: string) {
  openModalexamine(true, { record, type })
  setModalexamine({ title })
}

//附件盖章
function handleStamp(record: any) {
  openStampModal(true, { record })
}

// 预览
async function handlePreview(val: string, e: Event) {
  e.preventDefault()
  if (!val) return message.error('没有找到文件路径，请联系管理员')
  const reg = /\.([0-9a-z]+)(?:[\?#]|$)/i
  const prefix = val.match(reg)?.[1]
  console.log(prefix)
  if (prefix && ['png', 'jpg', 'jpge', 'gif'].includes(prefix)) {
    createImgPreview({ imageList: [val], maskClosable: true, defaultWidth: 500 })
  } else if (prefix && ['doc', 'docx', 'ppt', 'pptx', 'xlsx', 'xls', 'pdf'].includes(prefix)) {
    openModal(true, { url: val, fileType: prefix })
  } else {
    window.open(val, '_block')
  }
}
</script>
