<template>
  <div>
    <BasicTable
      :data-cachekey="routePath"
      @register="registerTable"
      :search-info="{ is_finance: routeName == '/financialDocuments/otherExpendFinance' ? 1 : 0 }"
    >
      <template #toolbar>
        <Tooltip>
          <template #title>选中采购订单生成付款单</template>
          <!-- <Button v-if="hasPermission([111])" @click="debouncedGenerate" :disabled="generateBtnStatus">生成付款单</Button> -->
        </Tooltip>
        <Button v-if="hasPermission([112])" type="primary" @click="handleCreate">
          <template #icon>
            <PlusOutlined />
          </template>
          新增
        </Button>
        <Button v-if="hasPermission([243])" type="primary" @click="onOpenCreate"> 新增分摊模式 </Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" :drop-down-actions="createDropDownActions(record)" :stopButtonPropagation="true" />
        </template>
        <template v-if="column.key === 'status'">
          <Tag :color="statusMap[record.status]?.color"> {{ statusMap[record.status]?.text || '-' }}</Tag>
        </template>
        <!-- <template v-if="column.key === 'finance_status'">
          <Tag :color="financestatus[record.status]?.color"> {{ financestatus[record.status]?.text }}</Tag>
        </template> -->
        <template v-if="column.key === 'urgent_level'">
          <Tag :color="urgentlevel[record.urgent_level]?.color"> {{ urgentlevel[record.urgent_level]?.text }}</Tag>
        </template>
        <template v-if="column.key === 'order'">
          <Tag :color="orderMap[record.order]?.color"> {{ orderMap[record.order]?.text }}</Tag>
        </template>
        <template v-if="column.key === 'manager_status'">
          <Tag :color="managerstatus[record.manager_status]?.color"> {{ managerstatus[record.manager_status]?.text }}</Tag>
        </template>
        <template v-if="column.key === 'is_check'">
          <Tag :color="checkMap[record.is_check]?.color"> {{ checkMap[record.is_check]?.text }}</Tag>
        </template>
        <template v-if="column.key === 'is_check2'">
          <Tag :color="checkMap[record.is_check2]?.color"> {{ checkMap[record.is_check2]?.text }}</Tag>
        </template>
        <template v-if="column.key === 'is_bind_fund'">
          <Tag :color="bindfund[record.is_bind_fund]?.color"> {{ bindfund[record.is_bind_fund]?.text }}</Tag>
        </template>
        <template v-if="column.key === 'cost'"> {{ '￥' }}{{ record.cost }} </template>
      </template>
      <template #footer="data">
        <div class="footer">支出金额合计(当前页)：{{ statisticsAmount(data) }}</div>
      </template>
    </BasicTable>

    <OtherExpendDrawer @register="registerDrawer" @success="handleSuccess" />
    <DetailsDrawer @register="registerOtherExpendDrawer" @relaod="handleSuccess" />
    <FinancialReview @register="registerFinancialReview" @relaod="handleSuccess" />
    <UploadModal @register="registerUploadModal" @relaod="handleSuccess" />
    <ShareDrawer @register="registershareDrawer" @relaod="handleSuccess" />
    <auditModal @register="registerauditModal" @relaod="reload" />
  </div>
</template>

<script setup lang="ts">
// import { ref } from 'vue'
import { useRoute } from 'vue-router'
import { Button, Tag, message, Tooltip } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { usePermission } from '/@/hooks/web/usePermission'
import { getOtherExpendList, deleteOtherIncome, getsetStatus, setApprove } from '/@/api/financialDocuments/otherExpend'
import { columns, formConfigFn, checkMap, orderMap, bindfund, urgentlevel, statusMap, managerstatus } from './datas/datas'
import OtherExpendDrawer from './components/OtherExpendDrawer.vue'
import DetailsDrawer from './components/DetailsDrawer.vue'
import { BasicTable, useTable, TableAction } from '/@/components/Table'
import type { ActionItem, EditRecordRow } from '/@/components/Table'
import { useDrawer } from '/@/components/Drawer'
import FinancialReview from './components/FinancialReview.vue'
// import { debounce } from 'lodash-es'
// import { addBatch } from '/@/api/financialDocuments/paymentOrder'
// import OtherExpendDrawer from './components/OtherExpendDrawer.vue'
// import DetailsDrawer from './components/DetailsDrawer.vue'
// import { addOrder } from '/@/api/financialDocuments/paymentOrder'
import UploadModal from './components/UploadModal.vue'
import { useModal } from '/@/components/Modal'
import { add } from '/@/utils/math'
import ShareDrawer from '../../baseData/shareManage/components/ShareDrawer.vue'
import { NEW_STATUS_FORMCONFIG } from '/@/const/status'
import { onMounted } from 'vue'
import auditModal from './components/auditModal.vue'
// import { isAllEqual } from '../../erp/purchaseOrder/datas/datas'
// import { useLoading } from '/@/components/Loading'

// const [openFullLoading, closeFullLoading] = useLoading({
//   tip: '加载中...'
// })
const route = useRoute()
const { name: routeName, path: routePath } = route
const { hasPermission } = usePermission()
//初始化
const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer()
const [registerOtherExpendDrawer, { openDrawer: openDetailsDrawer, setDrawerProps: setDetailsDrawerProps }] = useDrawer()
const [registerFinancialReview, { openDrawer: openexamineDrawer }] = useDrawer()
const [registerauditModal, { openModal: openauditModal, setModalProps: setauditModalProps }] = useModal()
const [registerTable, { reload, deleteTableDataRecord, clearSelectedRowKeys, setProps }] = useTable({
  title: '其他支出单列表',
  api: getOtherExpendList,
  columns,
  searchInfo: {
    order: null,
    is_finance: routeName == '/financialDocuments/otherExpendFinance' ? 1 : 0
  },
  showTableSetting: true,
  beforeFetch: (params) => {
    return { ...params, mapOrder: undefined }
  },
  afterFetch: () => {
    clearSelectedRowKeys()
  },
  useSearchForm: true,
  formConfig: {
    ...NEW_STATUS_FORMCONFIG,
    //自动展开行
    fieldMapToTime: [['check_at', ['check_at_start', 'check_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]],
    schemas: formConfigFn(routeName === '/financialDocuments/otherExpendFinance' ? 1 : 0) as any //这里还不如直接定义any类型
    // resetFunc: (): any => {
    //   clearSelectedRowKeys()
    //   reload()
    // },
    // submitFunc: (): any => {
    //   clearSelectedRowKeys()
    //   reload()
    // }
  },
  // rowSelection: {
  //   type: 'checkbox'
  // },
  actionColumn: hasPermission([113, 114, 115, 116, 226, 224, 305, 306, 713, 714])
    ? {
        width: 250,
        title: '操作',
        dataIndex: 'action',
        fixed: 'right'
      }
    : void 0,
  rowKey: 'id'
  // rowSelection: {
  //   onChange: handleChange,
  //   getCheckboxProps: (record) => {
  //     if (record.is_check !== 1) {
  //       return { disabled: true }
  //     } else {
  //       return { disabled: false }
  //     }
  //   }
  // }
})
onMounted(() => {
  setProps({
    formConfig: {
      ...NEW_STATUS_FORMCONFIG,
      schemas: formConfigFn(routeName === '/financialDocuments/otherExpendFinance' ? 1 : 0, { setProps }),
      //自动展开行
      fieldMapToTime: [['check_at', ['check_at_start', 'check_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]]
    }
  })
})

// 生成付款单
// const debouncedGenerate = debounce(handleGenerate, 200)
// async function handleGenerate() {
//   // const res = ref()
//   try {
//     openFullLoading()
//     const selectRow = getSelectRows().map((item) => {
//       item.sales.map((salesItem) => {
//         if (!salesItem.is_cancel) {
//           return salesItem
//         }
//       })
//       if (item.sales.length > 0) {
//         return item
//       }
//     })
//     if (!isAllEqual(selectRow, 'account')) return message.error('不同账号或部门不能生成同一张付款单！')

//     const worksData: Recordable[] = []
//     selectRow.forEach((item) =>
//       item.sales.forEach((salesItem) => {
//         if (!salesItem.is_cancel) {
//           worksData.push({
//             doc_id: salesItem.doc_id,
//             work_id: salesItem.work_id,
//             amount: salesItem.amount,
//             supplier_id: salesItem.supplier_id,
//             client_id: salesItem.client_id
//           })
//         }
//       })
//     )
//     await addBatch({
//       works: worksData,
//       is_allot: 0,
//       clause: 4,
//       dept_id: selectRow[0].dept_id,
//       is_finance: routeName === '/financialDocuments/otherExpendFinance' ? 1 : 0
//     })
//   } catch (error) {
//     console.log(error)
//     // message.error('生成付款单失败!')
//   } finally {
//     closeFullLoading()
//   }
// }

// 选中
// const generateBtnStatus = ref(true)

// function handleChange() {
//   if (getSelectRows().length == 0) {
//     generateBtnStatus.value = true
//   } else {
//     generateBtnStatus.value = false
//   }
// }
//action
// 底部统计函数
function statisticsAmount(data) {
  return data.reduce((total, item) => add(total, item.amount), 0)
}

/**
 * 检查记录的审核类型
 * @param {Object} record - 要检查的记录对象
 * @returns {string} - 审核类型: 'full' | 'manager' | 'admin' | 'direct'
 */
function getAuditType(record) {
  const hasValidSales = Array.isArray(record?.sales) && record.sales.length > 0
  if (!hasValidSales) return 'admin'

  // 1. 装修费/展厅费用 - 走三个审核（行政+总经理助理+总经理+审核）
  const hasDecorationAccount = record.sales.some((item) => item.account_name?.trim() === '营业费用-店铺成本-装修费/展厅费用')
  if (hasDecorationAccount) return 'full'

  // 2. 租金或实收资本 - 只有总经理助理+总经理+审核
  const hasManagerOnlyAccounts = record.sales.some((item) => ['营业费用-店铺成本-租金', '实收资本'].includes(item.account_name?.trim()))
  if (hasManagerOnlyAccounts) return 'manager'

  // 3. 其他指定科目 - 走行政审核+审核
  const ADDITIONAL_ACCOUNTS = [
    '营业费用-办公成本-办公费用',
    '营业费用-固定资产-工作手机电脑',
    '营业费用-店铺成本-建店成本',
    '营业费用-办公成本-物料/低值易耗品',
    '销售费用-市场推广费-车辆费用',
    '营业费用-店铺成本-水电费',
    '营业费用-固定资产-设备家具器具',
    '营业费用-办公成本-维护修理费'
  ]
  const hasAdditionalAccounts = record.sales.some((item) => ADDITIONAL_ACCOUNTS.includes(item.account_name?.trim()))
  if (hasAdditionalAccounts) return 'admin'

  // 4. 其他所有科目 - 直接走最终审核
  return 'direct'
}

/**
 * 检查是否需要总经理审核流程
 */
function needsManagerAudit(record) {
  const auditType = getAuditType(record)
  return auditType === 'full' || auditType === 'manager'
}

/**
 * 获取当前应该显示的审核按钮类型
 * @param {Object} record - 记录对象
 * @returns {string} - 当前审核阶段: 'admin' | 'manager_assistant' | 'manager' | 'final' | 'none'
 */
function getCurrentAuditStage(record) {
  const auditType = getAuditType(record)

  // 如果状态不是1，不显示任何审核按钮
  if (record.status !== 1) return 'none'

  switch (auditType) {
    case 'full': // 完整流程：行政 → 总经理助理 → 总经理 → 最终审核
      if (record.administration_status === 0 || record.administration_status === 3) {
        return 'admin'
      } else if (record.administration_status === 1 && (record.manager_status === 0 || record.manager_status === 3)) {
        return 'manager_assistant'
      } else if (record.administration_status === 1 && record.manager_status === 1) {
        return 'manager'
      } else if (record.administration_status === 1 && record.manager_status === 2) {
        return 'final'
      }
      break

    case 'manager': // 总经理流程：总经理助理 → 总经理 → 最终审核
      if (record.manager_status === 0 || record.manager_status === 3) {
        return 'manager_assistant'
      } else if (record.manager_status === 1) {
        return 'manager'
      } else if (record.manager_status === 2) {
        return 'final'
      }
      break

    case 'admin': // 行政流程：行政 → 最终审核
      if (record.administration_status === 0 || record.administration_status === 3) {
        return 'admin'
      } else if (record.administration_status === 1) {
        return 'final'
      }
      break

    case 'direct': // 直接审核：仅最终审核
      return 'final'

    default:
      return 'none'
  }

  return 'none'
}

function createActions(record: EditRecordRow): ActionItem[] {
  let editButtonList: ActionItem[] = [
    {
      icon: 'ant-design:check-outlined',
      label: '确认',
      disabled: record.is_approve == 1 || record.manager_status == 3 || record.manager_status == 4,
      popConfirm: {
        okText: '确定',
        title: '确定确定后将无法进行编辑操作，是否继续？',
        cancelText: '取消',
        placement: 'left',
        confirm: handleconfirm.bind(null, record, 'confirm')
      },
      ifShow: hasPermission([305]) && !(record.is_approve == 1 || record.manager_status == 3 || record.manager_status == 4)
    },
    {
      icon: 'ant-design:check-outlined',
      label: '主管审批',
      disabled: !(record.status == 0 && record.is_approve == 1),
      popConfirm: {
        okText: '确定',
        title: '确定将订单状态设置成已执行中状态吗',
        cancelText: '取消',
        placement: 'left',
        confirm: handleexamine.bind(null, record, 'execute')
      },
      ifShow: hasPermission([224]) && record.status == 0 && record.is_approve == 1
    },
    {
      icon: 'ant-design:check-outlined',
      label: '总经理助理审核',
      disabled:
        getCurrentAuditStage(record) === 'manager_assistant' &&
        (!(record.status == 1 && record.is_check !== 2 && record.is_check2 == 0 && record.manager_status == 0) ||
          (record.is_check === 1 && record.is_check2 == 0 && record.manager_status == 0)),
      onClick: handleaid.bind(null, record, 'manager_assistant'),
      ifShow: hasPermission([713]) && getCurrentAuditStage(record) === 'manager_assistant'
    },
    {
      icon: 'ant-design:check-outlined',
      label: '总经理审核',
      disabled:
        getCurrentAuditStage(record) === 'manager' &&
        (!(record.status == 1 && record.is_check !== 2 && record.is_check2 == 0 && record.manager_status == 1) ||
          (record.is_check === 1 && record.is_check2 == 0 && record.manager_status == 1)),
      onClick: handleaid.bind(null, record, 'manager'),
      ifShow: hasPermission([714]) && getCurrentAuditStage(record) === 'manager'
    },
    {
      icon: 'ant-design:check-outlined',
      label: '行政审核',
      disabled:
        getCurrentAuditStage(record) === 'admin' &&
        (!(record.status == 1 && record.is_check !== 2 && record.is_check2 == 0 && record.administration_status == 0) ||
          (record.is_check === 1 && record.is_check2 == 0 && record.administration_status == 0)),
      onClick: handleaid.bind(null, record, 'admin'),
      ifShow: hasPermission([749]) && getCurrentAuditStage(record) === 'admin'
    },
    {
      icon: 'ant-design:file-search-outlined',
      label: '审核',
      // disabled: record.status !== 0,
      onClick: handleexamines.bind(null, record),
      ifShow: hasPermission([114]) && getCurrentAuditStage(record) === 'final',
      disabled: getCurrentAuditStage(record) !== 'final'
    },

    {
      icon: 'clarity:note-edit-line',
      label: '编辑',
      onClick: handleUpdate.bind(null, record),
      disabled: shouldEnableAction(record),
      ifShow: hasPermission([115]) && !shouldEnableAction(record)
    }
  ]
  if (record.has_fund == 1) {
    editButtonList.forEach((item) => {
      item.disabled = true
    })
  }

  return editButtonList
}

function shouldEnableAction(record) {
  // 前两个条件的公共部分
  const commonCondition = record.is_check2 !== 2 && record.is_check !== 2

  // 前两个条件的合并
  const firstTwoConditions = commonCondition && (record.is_approve === 1 || record.status !== 0)

  // 第三个条件
  const thirdCondition = record.is_check === 2 && record.status === 16

  // 最终结果
  return firstTwoConditions || thirdCondition
}
function createDropDownActions(record: EditRecordRow): ActionItem[] {
  let editButtonList: ActionItem[] = [
    {
      icon: 'ant-design:eye-outlined',
      label: '详情',
      onClick: handleDetail.bind(null, record),
      ifShow: hasPermission([113])
    },
    {
      icon: 'ant-design:delete-outlined',
      color: 'error',
      label: '删除',
      popConfirm: {
        title: '是否确认删除',
        placement: 'left',
        confirm: handleDelete.bind(null, record),
        disabled: record.is_approve == 1
      },
      // disabled: record.status !== 0 && record.is_check !== 2,
      ifShow: hasPermission([116])
    },
    {
      icon: 'ant-design:upload-outlined',
      color: 'error',
      label: '附件',
      tooltip: '旧erp数据上传附件',
      onClick: handleUppload.bind(null, record)
    },
    {
      icon: 'ant-design:disconnect-outlined',
      label: '取消主管审批',
      // disabled: record.status !== 1 || record.is_check !== 0,
      popConfirm: {
        okText: '确定',
        title: '确定修改订单状态设置成待执行状态吗',
        cancelText: '取消',
        placement: 'left',
        confirm: handleexamine.bind(null, record, 'cancel'),
        disabled: (needsManagerAudit(record) && record.manager_status !== 0) || record.status !== 1 || record.is_check !== 0
      },
      ifShow: hasPermission([226])
    },
    {
      icon: 'ant-design:disconnect-outlined',
      label: '取消确认',
      // disabled: record.status !== 1 || record.is_check !== 0,
      popConfirm: {
        okText: '确定',
        title: '确定修改订单状态恢复成待确认状态吗',
        cancelText: '取消',
        placement: 'left',
        confirm: handleconfirm.bind(null, record, 'cancel'),
        disabled: !(record.is_approve == 1 && record.status == 0)
      },
      ifShow: hasPermission([306])
    }
  ]
  if (record.has_fund == 1) {
    editButtonList.forEach((item) => {
      if (item.label !== '详情') {
        item.disabled = true
      }
    })
  }

  return editButtonList
}

//创建
function handleCreate() {
  openDrawer(true, {
    isUpdate: false,
    type: 'add'
  })
  setDrawerProps({ title: '新增支出单' })
}
//更新
function handleUpdate(record) {
  openDrawer(true, {
    isUpdate: true,
    record,
    type: 'edit'
  })
  setDrawerProps({ title: '更新支出单信息' })
}
//删除
async function handleDelete(record) {
  await deleteOtherIncome({ id: record.id })
  deleteTableDataRecord(record.id)
  reload()
}
//详情
function handleDetail(record) {
  setDetailsDrawerProps({ title: '其他支出单详情' })
  openDetailsDrawer(true, {
    record,
    type: 'detail'
  })
}

async function handleSuccess() {
  clearSelectedRowKeys()
  await reload()
}
//财务审核
async function handleexamines(record: any) {
  openexamineDrawer(true, { record })
}

//单据审核
async function handleexamine(record: any, type: string) {
  if (record.account && record.account_name) {
    const msg: any = await getsetStatus({ id: record.id, status: type === 'execute' ? 1 : 0 })
    if (msg.news === 'success') {
      message.success('执行成功')
      reload()
    }
  } else {
    message.error('请先填写账号与账号名称')
  }
}
//status

// 旧erp数据上传附件
const [registerUploadModal, { openModal: openModalUplad }] = useModal()
function handleUppload(record) {
  openModalUplad(true, record)
}

// 确定
async function handleconfirm(record, key) {
  await setApprove({ id: record.id, is_approve: key == 'confirm' ? 1 : 0 })
  reload()
}
//新增分摊
const [registershareDrawer, { openDrawer: openshareDrawer }] = useDrawer()
function onOpenCreate() {
  setDrawerProps({ title: '新增分摊管理' })
  openshareDrawer(true, {
    type: 'create'
  })
}

//新增审核
function handleaid(record, type) {
  openauditModal(true, { record, type })
  setauditModalProps({
    title: type == 'admin' ? '行政审核' : record.manager_status == 0 ? '总经理助理审核' : '总经理审核'
  })
}
</script>
<style scoped lang="less">
.footer {
  font-size: 16px;
  font-weight: bold;
}
</style>
