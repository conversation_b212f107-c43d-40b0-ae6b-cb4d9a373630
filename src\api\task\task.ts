// import { BasicPageParams, BasicFetchResult } from '/@/api/model/baseModel'
import { defHttp, v2Http } from '/@/utils/http/axios'

enum Api {
  GetTaskList = '/erp/project/wt/getList',
  GetTaskadd = '/erp/project/wt/add',
  GetTaskupdate = '/erp/project/wt/update',
  GetPageList = '/erp/production/getPageList'
}

export const getTaskList = (params?: {}) => defHttp.get({ url: Api.GetTaskList, params })
export const getTaskadd = (params?: {}) => defHttp.post({ url: Api.GetTaskadd, params })
export const getTaskupdate = (params?: {}) => defHttp.post({ url: Api.GetTaskupdate, params })
export const getPageList = (params?: {}) => v2Http.get({ url: Api.GetPageList, params })
