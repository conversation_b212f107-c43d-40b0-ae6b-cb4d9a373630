import type { BasicColumn, FormSchema } from '/@/components/Table'
import { h } from 'vue'
import { getDeptTree } from '/@/api/admin/dept'
import { getErpSupplier } from '/@/api/commonUtils'
import { useMapStoreWithOut } from '/@/store/modules/commonMap'
import { getStaffList } from '/@/api/baseData/staff'
import { Tag } from 'ant-design-vue'
// import dayjs from 'dayjs'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { formateerNotCurrency } from '/@/utils/erp/formatterPrice'
import { mapStatus as mapRetreatStatus, mapRetreatOrderType } from '/@/views/erp/retreat/datas/datas'
import { isNull, isNullOrUnDef } from '/@/utils/is'
import { paymentType, typeOfRefundNote } from '/@/views/financialDocuments/refund/datas/fn'
import { useI18n } from '/@/hooks/web/useI18n'
import { isUndefined } from 'lodash-es'
import { changeMapToOptions, DIVIDER_SCHEMA, GET_STATUS_SCHEMA } from '/@/const/status'
import { cpgetList } from '/@/api/erp/purchaseOrder'

const { t, tm } = useI18n()

const commonMap = useMapStoreWithOut()

export const excelHeader = ['Article', '替换号', '订单号', '单价', '采购入库数量', '供应商名称']

export const mapStatus = {
  0: { label: '待执行', value: 1, color: 'blue' },
  1: { label: '执行中', value: 2, color: 'green' },
  15: { label: '已结束', value: 3, color: 'red' }
}

/** 是否驳回 */
export const mapReject = {
  1: { label: '正常', color: '' },
  2: { label: '驳回', color: 'red' }
}

/** 主管审批 */
export const approve = {
  0: { label: '未审批', color: '' },
  1: { label: '已批准', color: 'red' }
}

/** 结算状态 */
export const mapAudit = {
  0: { label: '未结算', color: '' },
  1: { label: '已结算', color: 'green' },
  2: { label: '待结算', color: 'skyblue' }
}
/** 入库状态 */
export const warehousingStatus = {
  0: { label: '未入库', color: '' },
  1: { label: '部分入库', color: 'green' },
  2: { label: '入库完成', color: 'skyblue' }
}

const Isticket = {
  1: { label: '是', color: 'green' },
  0: { label: '否', color: 'red' }
}
// const gbuiderstatus = {
//   0: { label: '待审核', color: '' },
//   1: { label: '通过', color: 'green' },
//   2: { label: '驳回', color: 'red' }
// }

//仓库员工只能看这些列
export const warehouseColumns: BasicColumn[] = [
  {
    title: '日期',
    dataIndex: 'created_at',
    width: 100,
    resizable: true
  },
  {
    title: '采购单号',
    dataIndex: 'strid',
    width: 250,
    resizable: true
  },
  {
    title: '部门',
    dataIndex: 'dept_name',
    width: 150,
    resizable: true
  }
]

export const getColumns = (): BasicColumn[] => {
  return [
    {
      title: '日期',
      dataIndex: 'created_at',
      width: 100,
      resizable: true
    },
    {
      title: '采购单号',
      dataIndex: 'strid',
      width: 250,
      resizable: true
    },
    {
      title: '紧急程度',
      dataIndex: 'urgent_level',
      width: 100,
      resizable: true,
      customRender: ({ text }) =>
        text ? useRender.renderTag(t(`tag.mapUrgentLevel.${text}.alias`), t(`tag.mapUrgentLevel.${text}.color`)) : '-'
    },
    {
      title: '供应商',
      dataIndex: 'supplier_name',
      width: 150,
      resizable: true
    },
    // {
    //   title: '生产完成',
    //   dataIndex: 'is_production_finish',
    //   width: 100,
    //   resizable: true,
    //   customRender: ({ text }) => {
    //     const map = {
    //       0: { color: 'error', label: '否' },
    //       1: { color: 'success', label: '是' }
    //     }
    //     return !isUndefined(text) && !isNull(text) ? useRender.renderTag(map[text].label, map[text].color) : '-'
    //   }
    // },

    {
      title: 'Gbuilder供应商',
      dataIndex: 'is_gbuilder',
      width: 150,
      resizable: true,
      customRender({ text }) {
        if (text === 0) {
          return h(Tag, { color: 'error' }, () => '否')
        } else if (text === 1) {
          return h(Tag, { color: 'success' }, () => '是')
        }
      }
    },
    {
      title: '质检特批',
      dataIndex: 'is_quality_approved',
      width: 150,
      resizable: true,
      customRender({ value }) {
        if (value === 0) {
          return h(Tag, { color: 'error' }, () => '否')
        } else if (value === 1) {
          return h(Tag, { color: 'success' }, () => '是')
        }
      }
    },
    // {
    //   title: '付款单号',
    //   dataIndex: 'paymentOrder_no',
    //   width: 100,
    //   resizable: true
    // },
    {
      title: '订单状态',
      dataIndex: 'status',
      width: 100,
      customRender: ({ value }) => (isNullOrUnDef(value) ? '-' : h(Tag, { color: mapStatus[value]?.color }, () => mapStatus[value]?.label))
    },
    {
      title: '确认状态',
      dataIndex: 'is_approve',
      width: 100,
      customRender: ({ value }) =>
        isNullOrUnDef(value) ? '-' : h(Tag, { color: approve[value || 0]?.color }, () => approve[value || 0]?.label)
    },
    // {
    //   title: 'Gbuilder审核',
    //   dataIndex: 'gbuider_status',
    //   width: 150,
    //   customRender: ({ value }) => {
    //     return isNullOrUnDef(value) ? '-' : h(Tag, { color: gbuiderstatus[value].color }, () => gbuiderstatus[value]?.label)
    //   }
    // },
    {
      title: '已入库金额',
      dataIndex: 'in_amount',
      width: 150,
      resizable: true
    },
    {
      title: '入库未付款金额',
      dataIndex: 'in_no_pay_amount',
      width: 150,
      resizable: true
    },
    {
      title: '财务审核',
      dataIndex: 'is_check',
      helpMessage: '财务驳回后请将采购单退货退款再建单',
      width: 150,
      resizable: true,
      customRender: ({ value }) => (isNullOrUnDef(value) ? '-' : h(Tag, { color: mapReject[value].color }, () => mapReject[value]?.label))
    },
    {
      title: '结算状态',
      dataIndex: 'is_audit',
      width: 150,
      resizable: true,
      customRender: ({ value }) => (isNullOrUnDef(value) ? '-' : h(Tag, { color: mapAudit[value].color }, () => mapAudit[value]?.label))
    },
    {
      title: '入库状态',
      dataIndex: 'warehousing_status',
      width: 150,
      resizable: true,
      customRender: ({ value }) =>
        isNullOrUnDef(value) ? '-' : h(Tag, { color: warehousingStatus[value].color }, () => warehousingStatus[value]?.label)
    },
    {
      title: '我司签约主体',
      dataIndex: 'contracting_party',
      resizable: true,
      width: 160
    },
    {
      title: '需求生产完成日期',
      dataIndex: 'request_status_at',
      width: 150,
      resizable: true
    },
    {
      title: '生产完成日期',
      dataIndex: 'production_finish_at',
      width: 150,
      resizable: true
    },
    {
      title: '结算日期',
      dataIndex: 'audit_at',
      width: 150,
      resizable: true
    },
    {
      title: '上传箱单日期',
      dataIndex: 'sale_file_at',
      width: 150,
      resizable: true
    },
    {
      title: '订单金额',
      dataIndex: 'total_price',
      width: 150,
      resizable: true
    },
    // {
    //   title: '总应付金额',
    //   dataIndex: 'cost',
    //   width: 150,
    //   resizable: true
    // },
    // {
    //   title: '实际应付金额',
    //   dataIndex: 'cost_actual',
    //   width: 150,
    //   resizable: true
    // },
    {
      title: '应付金额',
      dataIndex: 'cost_left',
      width: 150,
      resizable: true
    },
    {
      title: '实付金额',
      dataIndex: 'paid_actual',
      width: 150,
      resizable: true,
      customRender: ({ record }) => record.work.paid_actual ?? '0.0000'
    },
    {
      title: '已付金额',
      dataIndex: 'paid',
      width: 150,
      helpMessage: '实付金额与已付金额不对等,应是本张销售单存在退货或退款金额',
      resizable: true
    },
    // {
    //   title: '退款金额',
    //   dataIndex: 'refund_amount',
    //   width: 150,
    //   resizable: true
    // },
    // {
    //   title: '退货金额',
    //   dataIndex: 'retreat_amount',
    //   width: 150,
    //   resizable: true
    // },
    {
      title: '结算金额',
      dataIndex: 'audit_amount',
      width: 150,
      resizable: true
    },
    {
      title: '结算日期',
      dataIndex: 'audit_at',
      width: 120,
      resizable: true
    },
    {
      title: '是否开票',
      dataIndex: 'is_ticket',
      width: 150,
      resizable: true,
      customRender: ({ value }) => (isNullOrUnDef(value) ? '-' : h(Tag, { color: Isticket[value].color }, () => Isticket[value]?.label))
    },
    {
      title: '开票类型',
      dataIndex: 'ticket',
      width: 150,
      resizable: true
    },
    {
      dataIndex: 'is_engineering',
      title: '是否工程订单',
      resizable: true,
      width: 150,
      customRender: ({ text }) => {
        return !isNull(text) && !isUndefined(text)
          ? useRender.renderTag(t(`tag.tagColor.${text}.label`), t(`tag.tagColor.${text}.color`))
          : '-'
      }
    },
    {
      title: '入库完成日期',
      dataIndex: 'warehousing_status_at',
      width: 150,
      resizable: true
    },
    {
      title: '入库包裹数',
      dataIndex: 'pkg_num',
      width: 100,
      resizable: true
    },
    {
      title: '项目负责人',
      dataIndex: 'sale_inCharge',
      width: 100,
      resizable: true
    },
    {
      title: '方案负责人',
      dataIndex: 'sale_program_incharge',
      width: 100,
      resizable: true
    },
    {
      title: '部门',
      dataIndex: 'dept_name',
      width: 150,
      resizable: true
      // format: (_text, record) => mapDeptList[record.dept_id]
      // customRender: ({ record }) => {
      //   return record.dept && record.dept.dept_name
      // }
    },
    // {
    //   title: '关联单据',
    //   dataIndex: 'relation_order_no',
    //   width: 100,
    //   resizable: true
    // },
    {
      title: '申请人',
      dataIndex: 'processor_name',
      width: 150,
      resizable: true
      // format: (_text, record) => mapAccountList[record.processor]
    },
    {
      title: '责任人',
      dataIndex: 'inCharge',
      width: 150,
      resizable: true,
      format: (_text, record) => commonMap.getMapPerson[record.work.inCharge]
    },
    {
      title: 'gbilder驳回备注',
      dataIndex: 'gbuider_status_remark',
      width: 300,
      resizable: true
    },
    {
      title: '附件',
      dataIndex: 'files',
      width: 200,
      resizable: true,
      customRender: ({ value }) => {
        if (isNull(value)) return '-'
        return h(
          'div',
          {},
          value
            ?.filter((item) => !isNull(item))
            .map((item) =>
              h(
                'div',
                {
                  style: 'cursor: pointer; color: #9090e9; overflow: hidden; text-overflow: ellipsis; white-space: nowrap',
                  onClick: (e) => {
                    e.stopPropagation()
                    window.open(item)
                  }
                },
                item.replace(/.*\/(.*?)$/, '$1')
              )
            )
        )
      }
    }

    // {
    //   title: '付款中金额',
    //   dataIndex: 'no_amount',
    //   width: 150,
    //   resizable: true
    // }
  ]
}
const status_schema = GET_STATUS_SCHEMA(changeMapToOptions(mapStatus))
export const searchFromSchemas: FormSchema[] = [
  status_schema,
  DIVIDER_SCHEMA,
  {
    field: 'strid',
    label: '采购单号',
    component: 'Input'
  },
  {
    field: 'dept_ids',
    label: '部门',
    component: 'ApiTreeSelect',
    componentProps: {
      api: getDeptTree,
      immediate: false,
      lazyLoad: true,
      maxTagCount: 3,
      treeCheckable: true,
      multiple: true,
      showCheckedStrategy: 'SHOW_ALL',
      treeSelectProps: {
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        optionFilterProp: 'name',
        treeDefaultExpandAll: true,
        showSearch: true,
        placeholder: '请选择',
        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
    }
  },
  {
    field: 'supplier_id',
    label: '供应商',
    component: 'PagingApiSelect',
    componentProps: () => {
      return {
        api: getErpSupplier,
        selectProps: {
          fieldNames: { key: 'key', value: 'id', label: 'name' },
          optionFilterProp: 'name',
          showSearch: true,
          placeholder: '请选择',
          allowClear: true
        },
        searchMode: true,
        pagingMode: true,
        pagingSize: 20,
        resultField: 'items'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'processor',
    label: '申请人',
    component: 'PagingApiSelect',
    componentProps: () => {
      return {
        searchMode: true,
        pagingMode: true,
        api: getStaffList,
        selectProps: {
          fieldNames: { key: 'key', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          allowClear: true,
          optionFilterProp: 'name'
        },
        pagingSize: 20,
        resultField: 'items'
      }
    },

    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'created_at',
    label: '日期',
    component: 'SingleRangeDate',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    field: 'is_check',
    label: '财务审核',
    component: 'Select',
    componentProps: {
      options: [
        { label: '正常', value: 1 },
        { label: '驳回', value: 2 }
      ]
    }
  },
  {
    field: 'puid',
    label: '商品编号',
    component: 'Input'
  },
  {
    field: 'warehousing_status',
    label: '入库状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '未入库', value: 0 },
        { label: '部分入库', value: 1 },
        { label: '入库完成', value: 2 }
      ]
    }
  },
  {
    field: 'cost',
    label: '应付金额',
    component: 'InputNumber',
    componentProps: {
      precision: 2,
      min: 0
    }
  },
  {
    field: 'is_audit',
    label: '结算状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '未结算', value: 0 },
        { label: '待结算', value: 2 },
        { label: '已结算', value: 1 }
      ]
    }
  },
  {
    field: 'urgent_level',
    label: '紧急程度',
    component: 'Select',
    componentProps: {
      options: tm('tag.urgentLevelList'),
      fieldNames: { key: 'value', value: 'value', label: 'alias' }
    }
  },
  {
    field: 'is_overdue',
    label: '是否逾期',
    component: 'Select',
    componentProps: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ]
    }
  },
  {
    field: 'is_pass_nopay',
    label: '入库未付款金额 > 0',
    component: 'Select',
    // labelWidth: 180,
    componentProps: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ]
    }
  },
  {
    field: 'is_engineering',
    label: '是否工程订单',
    component: 'Select',
    componentProps: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ]
    }
  },
  {
    field: 'project_number ',
    label: '项目号',
    component: 'Input'
  },
  {
    field: 'contracting_party',
    label: '我司签约主体',
    component: 'PagingApiSelect',
    componentProps: {
      api: cpgetList,
      selectProps: {
        fieldNames: { key: 'key', value: 'name', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        allowClear: true,
        optionFilterProp: 'name'
      },
      resultField: 'items'
    }
  },
  {
    field: 'is_sale_file',
    label: '是否上传箱单',
    component: 'Select',
    componentProps: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ]
    }
  },
  {
    field: 'audit_at',
    label: '结算日期',
    component: 'SingleRangeDate',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  }
]
//退货columns
export const getColumnsRefundexport: BasicColumn[] = [
  {
    title: '单号',
    dataIndex: 'strid',
    width: 200,
    resizable: true
  },
  {
    title: '名称',
    dataIndex: 'name',
    width: 350,
    resizable: true
  },
  {
    title: '退货类型',
    dataIndex: 'type',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(mapRetreatOrderType[value].label, mapRetreatOrderType[value].color)
    }
  },
  {
    title: '订单状态',
    dataIndex: 'status',
    width: 120,
    customRender: ({ value }) => {
      return h(Tag, { color: mapRetreatStatus[value].color }, () => mapRetreatStatus[value].label)
    },
    resizable: true
  },
  // {
  //   title: '供应商名称',
  //   dataIndex: 'supplier_name',
  //   width: 120,
  //   resizable: true
  // },
  {
    title: '退货总金额',
    dataIndex: 'total_price',
    width: 200,
    resizable: true
  },
  // {
  //   title: '客户',
  //   dataIndex: 'client_name',
  //   width: 120,
  //   resizable: true
  // },
  // {
  //   title: '申请人名称',
  //   dataIndex: 'applicant_name',
  //   width: 120,
  //   resizable: true
  // },
  // {
  //   title: '负责人名称',
  //   dataIndex: 'inCharge_name',
  //   width: 120,
  //   resizable: true
  // },
  {
    title: '部门名称',
    dataIndex: 'department',
    width: 120,
    resizable: true
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: 200,
    resizable: true
  }
]
//退款columns
export const getColumnsRefund: BasicColumn[] = [
  {
    title: '单号',
    dataIndex: 'strid',
    width: 250
  },
  {
    title: '款项类型',
    dataIndex: 'type',
    width: 120,
    customRender: ({ text }) => {
      return paymentType(text)
    }
  },
  {
    title: '退款单类型',
    dataIndex: 'order',
    width: 120,
    customRender: ({ text }) => {
      return typeOfRefundNote(text)
    }
  },
  {
    title: '金额',
    dataIndex: 'amount',
    customRender: ({ value }) => formateerNotCurrency.format(value)
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 120,
    customRender: ({ record }) => {
      return h(Tag, { color: mapStatus[record.status] ? mapStatus[record.status].color : '' }, () =>
        mapStatus[record.status] ? mapStatus[record.status].label : ''
      )
    }
  },
  {
    title: '客户名称',
    dataIndex: 'client_name'
  },
  {
    title: '创建人',
    dataIndex: 'creator_name'
  },
  {
    title: '审核人名称',
    dataIndex: 'auditor_name'
  },
  {
    title: '创建时间',
    dataIndex: 'created_at'
  }
]

//判断 getSelectRows()里的每个元素的supplier_id是否都相同或dept_id都相同
export function isAllEqual(arr: Recordable[], key: 'supplier_id' | 'contracting_party' | 'client_id' | 'account' = 'supplier_id') {
  if (arr.length === 0) {
    return false // 如果数组为空，则返回 false
  }

  const firstItem = arr[0]

  // 检查数组中所有元素的 supplier_id 是否都相同
  const isSupplierIdEqual = arr.every((v) => v[key] === firstItem[key])

  // 检查数组中所有元素的 dept_id或客户 是否都相同
  const isDeptIdEqual = arr.every((v) => v.dept_id === firstItem.dept_id)
  const ticket = arr.every((v) => v.ticket === firstItem.ticket)
  console.log(isSupplierIdEqual, isDeptIdEqual, ticket)

  return isSupplierIdEqual && isDeptIdEqual && ticket
}

// gbuilder
export const gbuilderColumns: FormSchema[] = [
  {
    field: 'gbuider_status',
    label: '状态',
    component: 'RadioButtonGroup',
    required: true,
    defaultValue: 1,
    componentProps: {
      options: [
        {
          label: '通过',
          value: 1
        },
        {
          label: '驳回',
          value: 2
        }
      ]
    }
  },
  {
    field: 'gbuider_status_remark',
    label: '驳回备注',
    component: 'InputTextArea',
    show(renderCallbackParams) {
      return renderCallbackParams.values.gbuider_status === 2
    },
    required(renderCallbackParams) {
      return renderCallbackParams.values.gbuider_status === 2
    },
    ifShow(renderCallbackParams) {
      return renderCallbackParams.values.gbuider_status === 2
    },
    componentProps: {
      placeholder: '请输入驳回原因'
    }
  }
]
