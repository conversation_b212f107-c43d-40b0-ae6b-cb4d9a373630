import { defHttp } from '/@/utils/http/axios'
import {
  GenerateQcReportParams,
  QualityDetectionItem,
  QualityDetectionParams,
  QualityDetectionReportParams,
  QualityDetectionItemReport
} from './modle/types'
import { BasicFetchResult } from '/@/api/model/baseModel'
import { QcItemListParams } from '/@/api/erp/modle/qcReportModel'
import { ItemRequestResponse } from '/@/views/erp/purchaseOrder/datas/types'

enum Api {
  GetQualityDetectionList = '/erp/qc/getWorkStatic',
  GenerateQcReport = '/erp/qc/update',
  GetQualityDetectionDetail = '/erp/qc/getList',
  DelQualityDetectionDetail = '/erp/qc/deleteReport',
  GetItemQcList = '/erp/qc/getItemQcList',
  SetQcAbnormalRemark = '/erp/qr/setQcAbnormalRemark',
  BatchSetStatus = '/erp/project/wk/setQcStatus'
}

export const getQualityDetectionList = (params?: QualityDetectionParams) =>
  defHttp.get<BasicFetchResult<QualityDetectionItem>>({ url: Api.GetQualityDetectionList, params })

export const generateQcReport = (data: GenerateQcReportParams) => defHttp.post<{ msg: string }>({ url: Api.GenerateQcReport, data })

export const getQualityDetectionReport = (params?: QualityDetectionReportParams) =>
  defHttp.get<BasicFetchResult<QualityDetectionItemReport>>({ url: Api.GetQualityDetectionDetail, params })

export const delQualityDetectionReport = (id: number) => defHttp.get({ url: Api.DelQualityDetectionDetail, params: { id } })

export const getItemQcList = (params: QcItemListParams) =>
  defHttp.get<BasicFetchResult<ItemRequestResponse>>({ url: Api.GetItemQcList, params })

export const setQcAbnormalRemark = (params?: { work_id: number; qc_abnormal_remark: string }) =>
  defHttp.get({ url: Api.SetQcAbnormalRemark, params })

export const batchSetStatus = (data) => defHttp.post({ url: Api.BatchSetStatus, data })
