{"name": "financial-reporting-system", "version": "1.0.0", "author": {"name": "mxx", "email": "<EMAIL>"}, "scripts": {"commit": "czg", "bootstrap": "pnpm install", "serve": "npm run dev", "dev": "vite", "build": "cross-env NODE_ENV=production UV_THREADPOOL_SIZE=1 NODE_OPTIONS=--max-old-space-size=4096 vite build && esno ./build/script/postBuild.ts", "build:test": "cross-env NODE_OPTIONS=--max-old-space-size=4096 vite build --mode test && esno ./build/script/postBuild.ts", "build:car": "cross-env NODE_OPTIONS=--max-old-space-size=4096 UV_THREADPOOL_SIZE=1 vite build --mode car && esno ./build/script/postBuild.ts", "build:old": "cross-env NODE_OPTIONS=--max-old-space-size=4096 UV_THREADPOOL_SIZE=1 vite build --mode old && esno ./build/script/postBuild.ts", "build:sp": "cross-env NODE_OPTIONS=--max-old-space-size=4096 UV_THREADPOOL_SIZE=1 vite build --mode sp && esno ./build/script/postBuild.ts", "build:stone": "cross-env NODE_OPTIONS=--max-old-space-size=4096 UV_THREADPOOL_SIZE=1 vite build --mode stone && esno ./build/script/postBuild.ts", "build:sptests": "cross-env NODE_OPTIONS=--max-old-space-size=4096 UV_THREADPOOL_SIZE=1 vite build --mode sptests && esno ./build/script/postBuild.ts", "build:pytests": "cross-env NODE_OPTIONS=--max-old-space-size=4096 UV_THREADPOOL_SIZE=1 vite build --mode pytests && esno ./build/script/postBuild.ts", "build:no-cache": "pnpm clean:cache && npm run build", "report": "cross-env REPORT=true npm run build", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "preview": "npm run build && vite preview", "preview:dist": "vite preview", "log": "conventional-changelog -p angular -i CHANGELOG.md -s", "clean:cache": "rimraf node_modules/.cache/ && rimraf node_modules/.vite", "clean:lib": "rimraf node_modules", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock}/**/*.{vue,ts,tsx}\" --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,json,ts,tsx,css,less,scss,vue,html,md}\"", "lint:style": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged", "test:unit": "jest", "test:gzip": "npx http-server dist --cors --gzip -c-1", "test:br": "npx http-server dist --cors --brotli -c-1", "npm:check": "npx npm-check-updates", "reinstall": "rimraf pnpm-lock.yaml && rimraf package.lock.json && rimraf node_modules && npm run bootstrap", "prepare": "husky install", "gen:icon": "esno ./build/generate/icon/index.ts", "publish:tests": "node ./publish/index.ts --mode tests", "publish:car": "node ./publish/index.ts --mode car", "publish:prod": "node ./publish/index.ts --mode prod", "publish:old": "node ./publish/index.ts --mode old", "publish:sp": "node ./publish/index.ts --mode sp", "publish:sptests": "node ./publish/index.ts --mode sptests", "publish:pytests": "node ./publish/index.ts --mode pytests"}, "dependencies": {"@ant-design/colors": "^7.0.0", "@ant-design/icons-vue": "^6.1.0", "@iconify/iconify": "^3.1.0", "@logicflow/core": "^1.2.1", "@logicflow/extension": "^1.2.1", "@vue/runtime-core": "^3.2.47", "@vueuse/core": "^9.13.0", "@zxcvbn-ts/core": "^2.2.1", "ant-design-vue": "3.2.16", "axios": "^1.3.4", "codemirror": "^5.65.3", "cropperjs": "^1.5.13", "crypto-js": "^4.1.1", "dayjs": "^1.11.7", "decimal.js": "^10.4.3", "echarts": "^5.4.2", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "intro.js": "^6.0.0", "jspdf": "^2.5.1", "jszip": "^3.10.1", "lodash-es": "^4.17.21", "mockjs": "^1.1.0", "modern-screenshot": "^4.4.39", "nprogress": "^0.2.0", "path-to-regexp": "^6.2.1", "pinia": "2.0.33", "print-js": "^1.6.0", "qrcode": "^1.5.1", "qs": "^6.11.1", "resize-observer-polyfill": "^1.5.1", "showdown": "^2.1.0", "sortablejs": "^1.15.0", "ssh2": "^1.15.0", "ssh2-sftp-client": "^10.0.3", "tinymce": "^5.10.7", "vditor": "^3.9.1", "vue": "3.2.47", "vue-i18n": "^9.2.2", "vue-json-pretty": "^2.2.4", "vue-router": "4.1.6", "vue-types": "^5.0.2", "vue3-tree-org": "^4.2.2", "vuedraggable": "^4.1.0", "vxe-table": "4.4.5", "vxe-table-plugin-export-xlsx": "3.0.4", "xe-utils": "3.5.11", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/cli": "^17.5.1", "@commitlint/config-conventional": "^17.4.4", "@iconify/json": "^2.2.45", "@plugin-web-update-notification/vite": "^1.7.1", "@purge-icons/generated": "^0.9.0", "@types/codemirror": "^5.60.7", "@types/crypto-js": "^4.1.1", "@types/fs-extra": "^11.0.1", "@types/inquirer": "^9.0.3", "@types/intro.js": "^5.1.1", "@types/lodash-es": "^4.17.7", "@types/mockjs": "^1.0.7", "@types/node": "^18.15.7", "@types/nprogress": "^0.2.0", "@types/qrcode": "^1.5.0", "@types/qs": "^6.9.7", "@types/showdown": "^2.0.0", "@types/sortablejs": "^1.15.1", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1", "@vitejs/plugin-vue": "^4.1.0", "@vitejs/plugin-vue-jsx": "^3.0.1", "@vue/compiler-sfc": "^3.2.47", "@vue/test-utils": "^2.3.2", "autoprefixer": "^10.4.14", "conventional-changelog-cli": "^2.2.2", "cross-env": "^7.0.3", "cz-git": "^1.6.1", "czg": "^1.6.1", "dotenv": "^16.0.3", "eslint": "^8.37.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.10.0", "esno": "^0.16.3", "fs-extra": "^11.1.1", "husky": "^8.0.3", "inquirer": "^9.1.5", "less": "^4.1.3", "lint-staged": "^13.2.0", "picocolors": "^1.0.0", "postcss": "^8.4.21", "postcss-html": "^1.5.0", "postcss-less": "^6.0.0", "prettier": "^2.8.7", "rimraf": "^4.4.1", "rollup": "^3.20.2", "rollup-plugin-visualizer": "^5.9.0", "sass": "1.63.6", "stylelint": "^15.4.0", "stylelint-config-recommended": "^11.0.0", "stylelint-config-recommended-vue": "^1.4.0", "stylelint-config-standard": "^32.0.0", "stylelint-order": "^6.0.3", "terser": "^5.16.8", "ts-node": "^10.9.1", "typescript": "^5.0.3", "unplugin-vue-setup-extend-plus": "^0.4.9", "vite": "^4.2.1", "vite-plugin-compression": "^0.5.1", "vite-plugin-mkcert": "^1.14.0", "vite-plugin-mock": "^2.9.6", "vite-plugin-progress": "^0.0.7", "vite-plugin-purge-icons": "^0.9.2", "vite-plugin-pwa": "^0.14.7", "vite-plugin-style-import": "^2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-windicss": "^1.8.10", "vite-vue-plugin-html": "^1.0.1", "vite-vue-plugin-theme": "^1.0.0", "vue-eslint-parser": "^9.1.1", "vue-tsc": "^1.2.0"}, "repository": {"type": "git", "url": "git+https://github.com/xingyuv/vue-vben-admin.git"}, "license": "MIT", "bugs": {"url": "https://github.com/xingyuv/issues"}, "homepage": "https://github.com/xingyuv", "engines": {"node": ">= 16.0.0", "pnpm": ">=8.1.0"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "{!(package)*.json,*.code-snippets,.!(browserslist)*rc}": ["prettier --write--parser json"], "package.json": ["prettier --write"], "*.vue": ["eslint --fix", "prettier --write", "stylelint --fix"], "*.{scss,less,styl,html}": ["stylelint --fix", "prettier --write"], "*.md": ["prettier --write"]}, "config": {"commitizen": {"path": "node_modules/cz-git"}}}