<template>
  <div>
    <BasicTable :data-cachekey="routePath" @register="registerTable" @expand="onExpand">
      <template #toolbar>
        <!-- <Tooltip title="用于取消勾选禁用">
          <a-button @click="handleGenerate" class="mr-8px">取消禁用 </a-button>
        </Tooltip> -->
        <Tooltip title="根据搜索条件导出">
          <a-button type="primary" @click="handleExportDetail" :loading="isBtnLoding" v-if="hasPermission([552])"
            ><cloud-download-outlined /> 导出明细
          </a-button>
        </Tooltip>
        <Dropdown v-if="hasPermission(733)">
          <template #overlay>
            <Menu @click="handleMenu">
              <!-- <MenuItem key="putDownload">拼柜包裹模板下载</MenuItem> -->
              <!-- <MenuItem v-if="hasPermission(493)" key="ExportPdf">导出包裹二维码</MenuItem> -->
              <MenuItem v-if="hasPermission(733)" key="Exportcomplex">灯饰二维码</MenuItem>
            </Menu>
          </template>
          <a-button type="primary">
            二维码导出
            <DownOutlined />
          </a-button>
        </Dropdown>

        <Tooltip title="勾选本页相同供应商的采购订单">
          <a-button v-if="hasPermission([334])" :loading="isBtnLoding" :disabled="generateBtnStatus" @click="handlecontract" class="mr-8px"
            >购销合同导出
          </a-button>
        </Tooltip>
        <!-- <Tooltip title="订单传输金蝶">
          <a-button v-if="hasPermission([248])" :disabled="generateBtnStatus" @click="debouncehandleF" class="mr-8px">传输金蝶 </a-button>
        </Tooltip> -->
        <Tooltip>
          <template #title>选中采购订单生成付款单</template>
          <!-- <Popconfirm placement="topRight" @confirm="handleGenerate" :disabled="generateBtnStatus"> -->
          <!-- <template #title>
              <p>请输入本次应付总金额：</p>
              <Input prefix="￥" suffix="RMB" type="number" v-model:value="payable" :max="purchaseAllAmount" min="1" />
            </template> -->
          <a-button
            v-if="hasPermission([81])"
            :loading="isBtnLoding"
            :disabled="generateBtnStatus"
            @click="handleBeforeGenerate('common')"
            class="mr-8px"
            >生成付款单
          </a-button>
          <!-- </Popconfirm> -->
        </Tooltip>
        <Tooltip>
          <template #title>选中采购订单生成付款单</template>
          <!-- <Popconfirm placement="topRight" @confirm="handleGenerate" :disabled="generateBtnStatus"> -->
          <!-- <template #title>
              <p>请输入本次应付总金额：</p>
              <Input prefix="￥" suffix="RMB" type="number" v-model:value="payable" :max="purchaseAllAmount" min="1" />
            </template> -->
          <a-button
            v-if="hasPermission([551])"
            :loading="isBtnLoding"
            :disabled="generateBtnStatus"
            @click="handleBeforeGenerate('Storage')"
            class="mr-8px"
            >入库未付金额付款
          </a-button>
          <!-- </Popconfirm> -->
        </Tooltip>
        <a-button @click="handleVerify" v-if="hasPermission([133])" :loading="isBtnLoding" :disabled="generateBtnStatus"
          >收付款结账审核</a-button
        >
        <Tooltip>
          <template #title>选中销售订单批量结算</template>
          <a-button @click="account" v-if="hasPermission([280])" :loading="isBtnLoding" :disabled="generateBtnStatus">订单结算</a-button>
        </Tooltip>
        <a-button v-if="hasPermission([57])" type="primary" @click="handleCreate"><plus-outlined />新增</a-button>
        <a-button @click="handlePrintAgr" v-if="hasPermission([195])" :loading="isBtnLoding">
          <printer-outlined />
          打印AGR
        </a-button>
        <Dropdown v-if="hasPermission([197])">
          <a class="ant-dropdown-link" @click.prevent>
            <a-button>
              <template #icon><download-outlined /> </template>AGR文件 <download-outlined />
            </a-button>
          </a>
          <template #overlay>
            <Menu @click="handleMenuClick">
              <MenuItem key="upload"><upload-outlined /> 导入AGR</MenuItem>
              <MenuItem key="export"><download-outlined /> 模板</MenuItem>
            </Menu>
          </template>
        </Dropdown>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :stopa-buttonPropagation="true"
            :actions="createActions(record)"
            :drop-down-actions="createDropDownActions(record)"
          />
        </template>
        <template v-if="column.key === 'imgs'">
          <TableImg :imgList="record.imgs ?? []" :simpleShow="true" />
        </template>
        <!-- <template v-if="column.key === 'paid_actual'">
          {{ record.work.paid_actual }}
        </template> -->
      </template>
      <template #expandedRowRender="{ record }" v-if="!isWarehouseRole">
        <div style="height: 300">
          <Tabs v-model:activekey="activekey" tab-position="left" :style="{ height: '300px' }" @tab-click="callback($event, record)">
            <TabPane forceRender tab="退款" key="0">
              <BasicTable @register="registerrefund" :columns="getColumnsRefund" />
            </TabPane>
            <TabPane forceRender tab="退货" key="1">
              <BasicTable @register="registerrefreat" :columns="getColumnsRefundexport" />
            </TabPane> </Tabs
        ></div>
      </template>
    </BasicTable>
    <PurchaseDrawer @register="registerPurchaseDrawer" @success="handleSuccess" />
    <PurchaseModal @register="registerModal" @success="handleSuccess" />
    <SupplemtModal @register="registersupModal" @success="handleSuccess" />
    <RetreatDrawer @register="registerRetreatDrawer" @success="reload" />
    <UploadModal @register="registerUploadModal" @success="handleSuccess" />
    <PrintNumberModal @register="registerPrintNumberModal" />
    <PrintAgrTableModal @register="registerPrintAgrTableModalModal" />
    <ImportAgrDrawer @register="registerImportAgrDrawer" @success="handleSuccess" />
    <AddInWarehouseDrawer @register="registerAddInWarehouseDrawer" @success="handleSuccess" />
    <AddModel @register="registerModel" @success="handleSuccess" @close="clearSelectedRowKeys" />
    <FlowtransferDrawer @register="registerFlowtransferDrawer" @success="handleSuccess" />
    <contractModal @register="registerContractModal" @success="handleSuccess" />
    <publicandprivateDrawer @register="registerpublicandprivateDrawer" @success="handleSuccess" />
    <handleExportcomplexPdfModal @register="registerExportcomplexPdf" />
  </div>
</template>

<script setup lang="tsx">
import { PlusOutlined, PrinterOutlined, UploadOutlined, DownloadOutlined, CloudDownloadOutlined } from '@ant-design/icons-vue'
import { BasicTable, useTable, TableAction, TableImg } from '/@/components/Table'
import PurchaseDrawer from './components/purchaseDrawer.vue'
import PurchaseModal from './components/PurchaseModal.vue'
import AddModel from './components/AddModel.vue'

import {
  getPurchaseOrderList,
  setPurchaseStatus,
  delPurchase,
  getPurChildRefund,
  getPurChildRetreat,
  getsetApprove,
  setisQualityApprovede,
  setIsAudit
  // setGbuiderStatus
} from '/@/api/erp/purchaseOrder'
import { h, ref, computed } from 'vue'
// import { BasicTable, useTable, TableAction } from '/@/components/Table'
import type { ActionItem } from '/@/components/Table'
import {
  getColumns,
  warehouseColumns,
  searchFromSchemas,
  getColumnsRefundexport,
  getColumnsRefund,
  isAllEqual,
  excelHeader
  // gbuilderColumns
} from './datas/datas'
import { useDrawer } from '/@/components/Drawer'
import { Tooltip, message, Tabs, TabPane, Menu, MenuItem, Dropdown, Modal } from 'ant-design-vue'
import { useMessage } from '/@/hooks/web/useMessage'
import { IRecord } from './datas/types'
import { usePermission } from '/@/hooks/web/usePermission'
import { useModal } from '/@/components/Modal'

import SupplemtModal from './components/supplemtModal.vue'
import RetreatDrawer from '/@/views/erp/saleOrder/components/CreateRetreatDrawer.vue'
import { verifyOrder } from '/@/api/erp/sales'
import UploadModal from './components/UploadModal.vue'
import PrintNumberModal from '../print/components/PrintNumberModal.vue'
import PrintAgrTableModal from '../print/components/PrintAgrTableModal.vue'
import ImportAgrDrawer from './components/ImportAgrDrawer.vue'
import FlowtransferDrawer from '../saleOrder/components/FlowtransferDrawer.vue'
import { onExpExcelTemplate } from '/@/utils/exportTemplate'
import { getRelatePurchaseList } from '/@/api/erp/inWarehouse'
import AddInWarehouseDrawer from '../mainInWarehouse/components/AddDrawer.vue'
import { useUserStore } from '/@/store/modules/user'
import { storeToRefs } from 'pinia'
import { useSaleOrderStore } from '/@/store/modules/saleOrder'
import { cloneDeep, isEmpty } from 'lodash-es'
import contractModal from './components/contractModal.vue'
// import { BasicForm, useForm } from '/@/components/Form'
import { useRoute } from 'vue-router'
import { exportFile } from '/@/api/erp/PurchaseTracking'
import { downloadByData } from '/@/utils/file/download'
import { NEW_STATUS_FORMCONFIG } from '/@/const/status'
import publicandprivateDrawer from './components/publicandprivateDrawer.vue'
import handleExportcomplexPdfModal from './components/ExportcomplexitempdfModal.vue'

const pageSearchInfo = ref({})
const route = useRoute()
const { path: routePath } = route
const pathname = window.location.pathname

pageSearchInfo.value = {}
if (window.history.state?.searchParams) {
  pageSearchInfo.value = window.history.state.searchParams
}
const { hasPermission } = usePermission()
const { createMessage } = useMessage()
const generateBtnStatus = ref(true)
const activekey = ref(0)
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
const isBtnLoding = ref(false)
const saleStore = useSaleOrderStore()
const { warehouseQualityStaffRoleValues } = storeToRefs(saleStore)

const isWarehouseRole = ref(warehouseQualityStaffRoleValues.value.includes(userInfo.value?.roleValue))

const getActionColumnWidth = computed(() => {
  if (isWarehouseRole.value) {
    return 100
  }

  let width = 100 // 基础宽度
  let buttonCount = 0

  // 计算主要按钮数量
  if (hasPermission([302])) buttonCount++ // 确认按钮
  if (hasPermission([494])) buttonCount++ // gbuilder审核按钮
  if (hasPermission([58])) buttonCount++ // 主管审批按钮
  if (hasPermission([60])) buttonCount++ // 编辑按钮

  // 根据按钮数量计算宽度
  if (buttonCount > 0) {
    width = Math.min(100 + buttonCount * 87.5, 450) // 每个按钮增加87.5px宽度，最大不超过450
  }

  return width
})

const [registerTable, { reload, getSelectRows, clearSelectedRowKeys, deleteTableDataRecord, getForm, setLoading }] = useTable({
  title: '采购订单',
  showIndexColumn: false,
  columns: isWarehouseRole.value ? warehouseColumns : getColumns(),
  api: getPurchaseOrderList,
  isTreeTable: isWarehouseRole.value ? false : true,
  accordion: true,
  // dataSource: [],
  actionColumn: {
    width: getActionColumnWidth.value,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right'
  },
  afterFetch: () => {
    clearSelectedRowKeys()
  },
  beforeFetch: async (params) => {
    let pageParams = {}
    if (!isEmpty(pageSearchInfo.value)) {
      const form = getForm()
      pageParams = {
        ...pageSearchInfo.value
      }
      await form.setFieldsValue(pageParams)
      pageSearchInfo.value = {}
    }
    return {
      ...params,
      ...pageParams
    }
  },
  showTableSetting: true,
  rowKey: 'id',
  useSearchForm: true,
  formConfig: {
    ...NEW_STATUS_FORMCONFIG,
    schemas: searchFromSchemas,
    resetFunc: (): any => {
      clearSelectedRowKeys()
    },
    fieldMapToTime: [
      ['created_at', ['created_at_start', 'created_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['request_status_at', ['request_status_at_start', 'request_status_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['production_finish_at', ['production_finish_at_start', 'production_finish_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['audit_at', ['audit_at_start', 'audit_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]
    ]
  },
  rowSelection: {
    type: 'checkbox',
    onChange: handleChange,
    getCheckboxProps: (record) => {
      if (record.status == 0) {
        return { disabled: true }
      } else {
        return { disabled: false }
      }
    }
  }
})

const [registerModal, { openModal }] = useModal()
const [registersupModal, { openModal: opensupModal }] = useModal()
const [registerPrintNumberModal, { openModal: openPrintNumberModal }] = useModal()
const [registerPrintAgrTableModalModal, { openModal: openPrintAgrTableModalModal }] = useModal()

const [registerPurchaseDrawer, { openDrawer, setDrawerProps }] = useDrawer()
const [registerImportAgrDrawer, { openDrawer: openAgrDrawer }] = useDrawer()
const [registerRetreatDrawer, { openDrawer: openRetreatDrawer, setDrawerProps: setRetreatDrawerProps }] = useDrawer()
const [registerAddInWarehouseDrawer, { openDrawer: openAddInWarehouseDrawer, setDrawerProps: setAddInWarehouseDrawerProps }] = useDrawer()
const [registerpublicandprivateDrawer, { openDrawer: openpublicandprivateDrawer, setDrawerProps: setpublicandprivateDrawerProps }] =
  useDrawer()

function createActions(record: IRecord): ActionItem[] {
  return [
    {
      icon: 'ant-design:check-outlined',
      label: '确认',
      popConfirm: {
        title: '确认后将无法编辑，且无法编辑，是否确定执行？',
        placement: 'left',
        confirm: handleSetApprove.bind(null, record, 'pass')
      },
      disabled: record.is_approve == 1,
      ifShow: hasPermission([302])
    },
    //  {
    //   icon: 'ant-design:check-outlined',
    //   label: 'gbuilder审核',
    //   popConfirm: {
    //     title: (
    //       <div>
    //         <div>确认后将无法编辑，且无法编辑，是否确定执行？</div>
    //         <div>如若驳回,请输入驳回备注</div>
    //         <div>
    //           <BasicForm
    //             ref={(el: any) => (formRef.value = el?.formActionType)}
    //             register={useForm}
    //             showActionButtonGroup={false}
    //             schemas={gbuilderColumns}
    //             baseColProps={{ span: 24 }}
    //           />
    //         </div>
    //       </div>
    //     ),
    //     confirm: handlegbuilder.bind(null, record)
    //   },
    //   disabled: record.is_approve !== 1 || record.gbuider_status > 0 || record.is_check == 2,
    //   ifShow: hasPermission([494])
    // },
    {
      icon: 'ant-design:check-outlined',
      label: '主管审批',
      popConfirm: {
        title: '审批后订单进入执行中状态,且无法取消，是否确定执行？',
        placement: 'left',
        confirm: handleSetStatus.bind(null, record)
      },
      disabled: record.is_approve !== 1 || (record.is_approve == 1 && record.status !== 0),
      ifShow: hasPermission([58])
    },
    {
      icon: 'clarity:note-edit-line',
      label: '编辑',
      onClick: handleEdit.bind(null, record),
      disabled: record.is_approve == 1,
      ifShow: hasPermission([60])
    }
  ] as ActionItem[]
}

function createDropDownActions(record: IRecord): ActionItem[] {
  return [
    {
      icon: 'ant-design:eye-outlined',
      label: '详情',
      onClick: handleDetail.bind(null, record),
      ifShow: hasPermission([59])
    },
    {
      icon: 'ant-design:upload-outlined',
      label: '附件上传',
      tooltip: '旧erp数据上传附件',
      onClick: handleUppload.bind(null, record),
      ifShow: hasPermission([59])
    },
    {
      icon: 'ant-design:delete-outlined',
      color: 'error',
      label: '删除采购单',
      popConfirm: {
        title: '删除后将无法恢复，且无法编辑修改，是否确定删除？',
        placement: 'left',
        confirm: handleDelete.bind(null, record),
        disabled: record.status > 0
      },
      ifShow: hasPermission([61])
    },
    {
      icon: 'ant-design:credit-card-filled',
      label: '订单金额追加',
      onClick: handlesupplement.bind(null, record),
      disabled: [0].includes(record.status) || record.is_audit == 1 || record.is_check == 2,
      ifShow: hasPermission([137])
    },
    {
      label: '退货',
      color: 'error',
      icon: 'ant-design:rollback-outlined',
      disabled: ![1, 15].includes(record.status) || ![0, 2, 3, 4].includes(record.qc_status),
      onClick: handleRetreat.bind(null, record, 'retreat'),
      ifShow: hasPermission([162])
    },
    {
      label: '退货再建单',
      color: 'error',
      icon: 'ant-design:rollback-outlined',
      popConfirm: {
        title: '一键退货功能后,新建退货单同时创建一张新的采购订单？',
        placement: 'left',
        confirm: handleRetreat.bind(null, record, 'purchase'),
        disabled: ![1, 15].includes(record.status) || ![0, 2].includes(record.qc_status)
      },
      ifShow: hasPermission([317])
    },
    {
      label: '打印',
      icon: 'ant-design:printer-outlined',
      onClick: handlePrint.bind(null, record),
      ifShow: hasPermission([196]),
      disabled: ![1, 15].includes(record.status)
    },
    {
      label: '添加入库单',
      icon: 'ant-design:file-add-outlined',
      onClick: handleAddInWarehouse.bind(null, record),
      disabled: ![1].includes(record.status),
      ifShow: hasPermission([221])
    },
    {
      label: '流水调拨',
      disabled: !(record.paid > 0 && record.is_check == 1 && record.is_audit !== 1),
      onClick: handleFlowtransfer.bind(null, record),
      ifShow: hasPermission([299])
    },
    {
      icon: 'ant-design:check-outlined',
      label: '取消确认',
      popConfirm: {
        title: '审核后将无法编辑，且无法编辑，是否确定执行？',
        placement: 'left',
        confirm: handleSetApprove.bind(null, record, 'cancel'),
        disabled: !(record.is_approve == 1 && record.status == 0)
      },
      ifShow: hasPermission([303])
    },
    {
      icon: 'ant-design:check-outlined',
      label: '质检特批',
      popConfirm: {
        title: '确定更改质检特批的状态？',
        placement: 'left',
        confirm: handleSetQuality.bind(null, record)
      },
      disabled: record.is_quality_approved == 1,
      ifShow: hasPermission([390])
    },
    {
      icon: 'ant-design:rollback-outlined',
      label: '结算更改',
      popConfirm: {
        title: '确定将订单待结算状态转为未结算状态？',
        placement: 'left',
        confirm: handleIsAudit.bind(null, record),
        disabled: ![1, 2].includes(record.is_audit)
      },
      ifShow: hasPermission([423])
    },
    {
      label: '公私单转换',
      onClick: handlepublid.bind(null, record),
      disabled: record.status == 0,
      ifShow: hasPermission([597])
    }
  ]
}

// 详情
function handleDetail(record: IRecord) {
  console.log(record, 'record')
  openDrawer(true, { record: cloneDeep(record), isUpdate: false, type: 'detail' })
  setDrawerProps({ title: '查看采购订单详情' })
}

// 修改
function handleEdit(record: IRecord) {
  openDrawer(true, { record, isUpdate: true, type: 'edit' })
  setDrawerProps({ title: '编辑采购订单' })
}

// 创建
function handleCreate() {
  openDrawer(true, { isUpdate: true, type: 'add' })
  setDrawerProps({ title: '新增采购订单' })
}

function handlePrintAgr() {
  openPrintAgrTableModalModal(true, { type: 'purchase' })
}

function handleMenuClick({ key }) {
  if (key === 'export') {
    onExpExcelTemplate(excelHeader)
  } else if (key === 'upload') {
    openAgrDrawer(true, {})
  }
}

function handleAddInWarehouse(record: IRecord) {
  setAddInWarehouseDrawerProps({ title: '新增入库单' })
  openAddInWarehouseDrawer(true, { isUpdate: true, type: 'add', page: 'purchase', record })
}

//补款
function handlesupplement(record: IRecord) {
  opensupModal(true, { record })
}

//退货
async function handleRetreat(record: IRecord, way: string) {
  const { items } = await getRelatePurchaseList({ id: record.id, is_wait: 1 })
  if (items.length === 0) return message.error('剩余可退货数量为0!')
  setRetreatDrawerProps({ title: '采购退货' })
  openRetreatDrawer(true, { record, type: 2, items, way })
}

// 旧erp数据上传附件
const [registerUploadModal, { openModal: openModalUplad }] = useModal()
function handleUppload(record) {
  openModalUplad(true, record)
}
//同步上传到金蝶
// const debouncehandleF = debounce(handleF, 500)
// async function handleF() {
//   const selectRow = getSelectRows()
//   let show = false
//   for (let item of selectRow) {
//     if (item.status == 0) {
//       message.warning('请选择执行中或之后的的采购单')
//       show = true
//       break
//     }
//   }
//   if (!show) {
//     const ids = ref<any>([])
//     selectRow.map((item: any) => {
//       ids.value.push(item.id)
//     })
//     const res = await postsendToJd({ ids: ids.value })
//     if (res.news === 'success') {
//       message.success('数据传输成功')
//     }
//   }
// }
/** 生成付款单 */
function handleBeforeGenerate(type: string) {
  const selectRow = getSelectRows()
  const show = ref(false)
  for (let item of selectRow) {
    if (item.is_audit == 1) {
      message.warning('请选择未结算的采购单')
      show.value = true
      break
    } else if (item.status == 0) {
      message.warning('请选择执行中或之后的的采购单')
      show.value = true
      break
    } else if (item.payment_type && item.payment_type !== 1) {
      message.warning('该采购订单已完成付款单最后一笔款或全款生成')
      show.value = true
      break
    } else if (item.in_no_pay_amount == 0 && type == 'Storage') {
      message.error('勾选的采购单存在入库未付款金额为0,无法生成付款单')
      show.value = true
      break
    }
    // if (item.is_check == 2) {
    //   message.warning('勾选的采购单存在财务驳回，请先检查采购单并编辑后再生成付款单')
    //   show.value = true
    //   break
    // } else
  }
  const validation = validateSelectedRows(selectRow)
  if (!validation.isValid) {
    message.error(validation.errorMessage!)
    return
  }

  if (!show.value) {
    if (!isAllEqual(selectRow)) return message.error('相同部门或相同供应商且是否开票及开票类型一致才能生产付款单！')
    openModal(true, {
      record: selectRow,
      supplierId: selectRow[0].supplier_id,
      dept_id: selectRow[0].dept_id,
      form: type
    })
  }
}
console.log(routePath)

function validateSelectedRows(selectRow: Recordable[]): { isValid: boolean; errorMessage?: string } {
  if (selectRow.length === 0) return { isValid: true } // 空数组默认视为一致

  const firstItem = selectRow[0]
  const { currency, exchange_rate, contracting_party } = firstItem

  for (const item of selectRow) {
    // 检查我司签约主体
    if (item.contracting_party !== contracting_party) {
      return {
        isValid: false,
        errorMessage: '勾选的采购单的我司签约主体不一致，无法生成付款单！'
      }
    }

    // 检查币种和汇率
    if (pathname === '/s/') {
      // 当 pathname 等于 '/s/' 时，同时判断 currency 和 exchange_rate
      if (item.currency !== currency || item.exchange_rate !== exchange_rate) {
        return {
          isValid: false,
          errorMessage: '勾选的采购单的币种不一致，无法生成付款单！'
        }
      }
    } else {
      // 当 pathname 不等于 '/s/' 时，只判断 currency
      if (item.currency !== currency) {
        return {
          isValid: false,
          errorMessage: '勾选的采购单的币种不一致，无法生成付款单！'
        }
      }
    }
  }

  return { isValid: true }
}

/** 选中 */
function handleChange() {
  if (getSelectRows().length == 0) {
    generateBtnStatus.value = true
  } else {
    generateBtnStatus.value = false
  }
}

//批量结算日期
const [registerModel, { openModal: openAddModal }] = useModal()
function account() {
  const selectData = getSelectRows()
  if (selectData.length < 1) return
  const show = ref(false)
  for (let item of selectData) {
    if (item.is_audit == 1) {
      message.warning('请选择未结算的采购单')
      show.value = true
      break
    }
  }
  const data = selectData.map((item) => {
    return { id: item.work_id }
  })
  if (!show.value) {
    openAddModal(true, data)
  }
}
//确认
async function handleSetApprove(record: IRecord, type: string) {
  if (Number(record.cost) > Number(record.sales_price) && type == 'pass') {
    Modal.confirm({
      title: '该订单采购总价大于销售总价，是否继续？',
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      async onOk() {
        await getsetApprove({ id: record.id, is_approve: type == 'pass' ? 1 : 0 })
        await reload()
        if (type === 'pass') handleGenderPayment(record)
      }
    })
  } else {
    await getsetApprove({ id: record.id, is_approve: type == 'pass' ? 1 : 0 })
    await reload()
    if (type === 'pass') handleGenderPayment(record)
  }
}

function handleGenderPayment(record) {
  Modal.confirm({
    title: '是否生成付款单？',
    onOk() {
      openModal(true, {
        record: [record],
        supplierId: record.supplier_id,
        dept_id: record.dept_id,
        is_gublider: 1
      })
    }
  })
}

async function handleSetStatus(record: IRecord) {
  console.log(record, 'record')
  try {
    if (Number(record.cost) > Number(record.sales_price)) {
      Modal.confirm({
        title: '该订单采购总价大于销售总价，是否继续？',
        okText: '确定',
        okType: 'danger',
        cancelText: '取消',
        async onOk() {
          const { news } = await setPurchaseStatus({ status: 1, id: record.id })
          await reload()
          news === 'success' ? createMessage.success('审核成功') : createMessage.error('审核失败')
        }
      })
    } else {
      const { news } = await setPurchaseStatus({ status: 1, id: record.id })
      news === 'success' ? createMessage.success('审核成功') : createMessage.error('审核失败')
      await reload()
    }
  } catch (e) {
    throw new Error(`${e}`)
  }
}

async function handleDelete(record: IRecord) {
  console.log(record, 'record')
  try {
    const { msg } = await delPurchase(record.id)
    if (msg === 'success') {
      deleteTableDataRecord(record.id)
      createMessage.success('删除成功')
      reload()
      return
    }
    createMessage.error('删除失败')
  } catch (e) {
    throw new Error(`${e}`)
  }
}

//点击打印
function handlePrint(Record: IRecord) {
  const record = { ...Record, purchase_strid: Record.strid, source_uniqid: Record.source_uniqid, dept_name: Record.dept_name }
  openPrintNumberModal(true, { record, page: 'purchase' })
}

const { createConfirm } = useMessage()
//结账核对
function handleVerify() {
  const selectData = getSelectRows()
  const show = ref(false)
  console.log(selectData, 'selectData')
  for (let item of selectData) {
    if (item.is_check == 2) {
      message.warning('请选择未审核的采购单')
      show.value = true
      break
    } else if (item.status == 0) {
      message.warning('请选择执行中或之后的的采购单')
      show.value = true
      break
    }
  }
  // if (selectData.length < 1) return
  if (!show.value) {
    createConfirm({
      iconType: 'warning',
      title: () => h('span', '结账核对'),
      content: () => h('span', '只有状态为执行中的订单才可以进行结账核对,确定继续结账核对吗?'),
      onOk: async () => {
        const data = selectData.map((item) => {
          return item.work_id
        })
        console.log(data)
        await verifyOrder({
          is_audit: 1,
          work_ids: data
        })
        handleSuccess()
      }
    })
  }
}

function handleSuccess() {
  clearSelectedRowKeys()
  reload()
}
// 退款
async function onExpand(expanded: boolean, record: IRecord): Promise<void> {
  console.log(record, 'record', expanded)
  const { items } = await getPurChildRefund({ work_id: record.work_id })
  setColumns(getColumnsRefund)
  setTableData(items)
}

async function callback(val, record) {
  const { items } = val == 0 ? await getPurChildRefund({ work_id: record.work_id }) : await getPurChildRetreat({ work_id: record.work_id })
  if (val == 0) {
    setColumns(getColumnsRefund)
    setTableData(items)
  } else if (val == 1) {
    eatsetColumns(getColumnsRefundexport)
    eatsettabbel(items)
  }
}
const [registerrefund, { setColumns, setTableData }] = useTable({
  maxHeight: 200,
  pagination: false
})
const [registerrefreat, { setColumns: eatsetColumns, setTableData: eatsettabbel }] = useTable({
  maxHeight: 200,
  pagination: false
})
//流水调拨
const [registerFlowtransferDrawer, { openDrawer: openFlowtransferDrawer }] = useDrawer()
function handleFlowtransfer(record) {
  openFlowtransferDrawer(true, { type: 2, record })
}

const handleExportDetail = async () => {
  try {
    setLoading(true)
    isBtnLoding.value = true
    const params = getForm()?.getFieldsValue()
    const response = await exportFile({ ...params, is_excel: 3 })
    //将二进制流转xlsx文件并下载
    downloadByData(response as any, `采购订单明细-${+new Date()}.xlsx`)
    createMessage.success('导出成功')
  } catch (err) {
    createMessage.error('导出失败')
    throw new Error(err)
  } finally {
    setLoading(false)
    isBtnLoding.value = false
  }
}

//购销合同导出
const [registerContractModal, { openModal: openContractModal }] = useModal()
function handlecontract() {
  const selectRow = getSelectRows()
  const ids = ref([])
  for (let item of selectRow) {
    ids.value.push(item.id)
  }
  if (!isAllEqual(selectRow, 'supplier_id')) return message.error('相同供应商才能生成同一张采购合同！')
  if (!isAllEqual(selectRow, 'contracting_party')) return message.error('相同签约主体才能生成同一张采购合同！')
  openContractModal(true, {
    ids: ids.value,
    supplier_id: selectRow[0].supplier_id
  })
}

//质检特批
function handleSetQuality(record) {
  try {
    setisQualityApprovede({ id: record.id, is_quality_approved: 1 })
    reload()
  } catch (e) {
    console.log(e)
  }
}

//反结算
async function handleIsAudit(record) {
  await setIsAudit({ work_id: record.work_id, is_audit: 0 })
  await reload()
}
//gbuilder
// const formRef = ref<any>()
// async function handlegbuilder(record) {
//   // if (Number(record.cost) > Number(record.sales_price)) {
//   //   Modal.confirm({
//   //     title: '该订单采购总价大于销售总价，是否继续？',
//   //     okText: '确定',
//   //     okType: 'danger',
//   //     cancelText: '取消',
//   //     async onOk() {
//   //       const formdata = await formRef.value?.validate()
//   //       setGbuiderStatus({ ...formdata, id: record.id })
//   //       reload()
//   //       formRef.value?.resetFields()
//   //     }
//   //   })
//   // } else {
//   const formdata = await formRef.value?.validate()
//   setGbuiderStatus({ ...formdata, id: record.id })
//   reload()
//   formRef.value?.resetFields()
//   // }
// }

//公私单
function handlepublid(record) {
  openpublicandprivateDrawer(true, { record })
  setpublicandprivateDrawerProps({ title: '公私单转换', showFooter: true, width: '90%' })
}

//二维码
const [registerExportcomplexPdf, { openModal: openExportcomplexPdf }] = useModal()

function handleMenu({ key }: { key: 'Exportcomplex' | 'download' }) {
  if (key === 'download' && isBtnLoding.value) return createMessage.error('正在下载中，请勿重复点击')
  handleExportcomplexModal()
}

function handleExportcomplexModal() {
  const Rows = getSelectRows()

  if (Rows.length === 0) return createMessage.error('请选择需要导出二维码的采购单！')
  const issupper = Rows.every((item) => item.supplier_id === Rows[0].supplier_id)
  if (!issupper) return createMessage.error('请选择相同供应商的采购单！')

  const newrows = Rows.map((item) => {
    return {
      strid: item.strid,
      items: item.item,
      dept_name: item.dept_name,
      source_uniqid: item.source_uniqid
    }
  })

  openExportcomplexPdf(true, {
    newrows
  })
}
</script>
